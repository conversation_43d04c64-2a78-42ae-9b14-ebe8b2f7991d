---
/**
 * Velocity Dashboard Page
 * Following Clean Code: Single responsibility, clear data flow
 */

import Layout from '../../components/ui/Layout.astro';
import BoardSelector from '../../components/velocity/BoardSelector.astro';
import ScrumAnalytics from '../../components/analytics/ScrumAnalytics.astro';
import KanbanAnalytics from '../../components/analytics/KanbanAnalytics.astro';

import VelocityProgressLoader from '../../components/ui/VelocityProgressLoader.astro';
import SprintIssuesModal from '../../components/velocity/SprintIssuesModal.astro';

// Fetch initial data server-side
let boards = [];
let error = null;

try {
  const response = await fetch(`${Astro.url.origin}/api/velocity/boards`);
  const data = await response.json();
  
  if (response.ok) {
    boards = data.boards || [];
  } else {
    error = data.error || 'Failed to load boards';
  }
} catch (e) {
  error = 'Network error loading boards';
}

// Select first board by default if available
const defaultBoardId = boards.length > 0 ? boards[0].id : null;
---

<Layout title="Velocity Dashboard - Jira Tool Analytics">
  <div class="velocity-page">
    <!-- Page Header -->
    <div class="page-header mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">
        Velocity Dashboard
      </h1>
      <p class="text-gray-600">
        Track sprint velocity and team performance across boards
      </p>
    </div>
    
    <!-- Error State -->
    {error && (
      <div class="error-banner bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex">
          <div class="text-red-400">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-800">
              {error}
            </p>
          </div>
        </div>
      </div>
    )}
    
    <!-- Main Content -->
    {!error && (
      <div class="velocity-dashboard">
        <!-- Board Selection -->
        <div class="board-selection-section mb-8">
          <BoardSelector 
            boards={boards}
            selectedBoardId={defaultBoardId}
            disabled={boards.length === 0}
          />
        </div>
        
        <!-- Analytics Content - Conditional based on board type -->
        <div class="analytics-content-section">
          <!-- Scrum Analytics (default view) -->
          <div id="scrum-analytics-view" class="analytics-view">
            <ScrumAnalytics boardId={defaultBoardId} loading={defaultBoardId ? true : false} />
          </div>

          <!-- Kanban Analytics (hidden by default) -->
          <div id="kanban-analytics-view" class="analytics-view hidden">
            <KanbanAnalytics boardId={defaultBoardId} loading={defaultBoardId ? true : false} />
          </div>
        </div>
        
        <!-- Loading overlay for dynamic updates -->
        <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div id="progress-loader-container">
            <VelocityProgressLoader 
              boardId={defaultBoardId || 'unknown'}
              stage="quick"
              progress={0}
              message="Initializing velocity analysis..."
              allowCancel={true}
            />
          </div>
        </div>
      </div>
    )}
  </div>
  
  <!-- Sprint Issues Modal -->
  <SprintIssuesModal sprint={null} isOpen={false} />
</Layout>

<script>
  // Client-side interactivity for dynamic board switching
  // Following Clean Code: Express intent, single responsibility per function
  
  let currentBoardId: string | null = null;





  // Functions removed - updateProgressLoader and createProgressLoaderHTML were unused


  

  
  /**
   * Shows loading overlay
   */
  function showLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.classList.remove('hidden');
    }
  }
  
  /**
   * Hides loading overlay
   */
  function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.classList.add('hidden');
    }
  }
  
  /**
   * Switches between Scrum and Kanban analytics views
   * Following Clean Code: Express intent, single responsibility
   */
  function switchAnalyticsView(boardType: string) {
    const scrumView = document.getElementById('scrum-analytics-view');
    const kanbanView = document.getElementById('kanban-analytics-view');

    if (!scrumView || !kanbanView) return;

    if (boardType === 'kanban') {
      // Show Kanban analytics, hide Scrum analytics
      scrumView.classList.add('hidden');
      kanbanView.classList.remove('hidden');
      console.log('Switched to Kanban analytics view');
    } else {
      // Show Scrum analytics (default for 'scrum', 'simple', or unknown types)
      kanbanView.classList.add('hidden');
      scrumView.classList.remove('hidden');
      console.log('Switched to Scrum analytics view');
    }
  }

  /**
   * Handles board change event - now only switches views without loading data
   * Following Clean Code: Event handling with clear flow
   */
  async function handleBoardChange(event: CustomEvent) {
    const { boardId, boardType } = event.detail;

    if (boardId === currentBoardId) return;

    currentBoardId = boardId;

    // Switch analytics view based on board type
    switchAnalyticsView(boardType);

    // Reset loading states and update component states without loading data
    if (boardType === 'kanban') {
      // Set board ID for Kanban analytics without loading data
      if ((window as any).kanbanAnalytics?.setBoardId) {
        (window as any).kanbanAnalytics.setBoardId(boardId);
      }
      // Reset Scrum analytics state for Kanban boards
      if ((window as any).scrumAnalytics?.resetLoadingState) {
        (window as any).scrumAnalytics.resetLoadingState(null);
      }
    } else {
      // For Scrum boards, clear Kanban board and disable filters
      if ((window as any).kanbanAnalytics?.setBoardId) {
        (window as any).kanbanAnalytics.setBoardId(null);
      }
      
      // Reset and update Scrum analytics state
      if ((window as any).scrumAnalytics?.resetLoadingState) {
        (window as any).scrumAnalytics.resetLoadingState(boardId);
      }
      
      // Update Scrum analytics filters state
      if ((window as any).scrumAnalyticsCycleTime?.updateCycleTimeFiltersState) {
        (window as any).scrumAnalyticsCycleTime.updateCycleTimeFiltersState(boardId, false);
      }
    }
    
    console.log(`Switched to ${boardType} analytics view for board: ${boardId} (data will load on tab interaction)`);
  }
  

  
  /**
   * Initializes the page without loading analytics data
   * Following Clean Code: Setup function with clear intent
   */
  async function initializePage() {
    // Setup initial view if board is pre-selected but don't load data
    const boardSelect = document.getElementById('board-select') as HTMLSelectElement;
    if (boardSelect && boardSelect.value) {
      currentBoardId = boardSelect.value;

      // Get board type from the selected option
      const selectedOption = boardSelect.options[boardSelect.selectedIndex];
      const boardTypeMatch = selectedOption?.text.match(/\((\w+)\)$/);
      const boardType = boardTypeMatch ? boardTypeMatch[1] : 'scrum';

      // Switch to appropriate analytics view
      switchAnalyticsView(boardType);

      // Update component states without loading data
      if (boardType === 'kanban') {
        if ((window as any).kanbanAnalytics?.setBoardId) {
          (window as any).kanbanAnalytics.setBoardId(currentBoardId);
        }
      } else {
        if ((window as any).scrumAnalytics?.resetLoadingState) {
          (window as any).scrumAnalytics.resetLoadingState(currentBoardId);
        }
        if ((window as any).scrumAnalyticsCycleTime?.updateCycleTimeFiltersState) {
          (window as any).scrumAnalyticsCycleTime.updateCycleTimeFiltersState(currentBoardId, false);
        }
      }

      console.log(`Initialized ${boardType} analytics view for board: ${currentBoardId} (data will load on user interaction)`);
    }
  }
  
  /**
   * Opens the sprint issues modal
   * Following Clean Code: Clear intent, parameter validation
   */
  function openSprintModal(sprintId: string, _sprintName: string) {
    if (!sprintId) {
      console.error('Sprint ID is required');
      return;
    }
    
    // Find the sprint data from the current velocity data
    const velocityData = (window as any).currentVelocityData;
    if (velocityData && velocityData.closedSprints) {
      const selectedSprint = velocityData.closedSprints.find((sv: any) => sv.sprint.id === sprintId);
      
      if (selectedSprint) {
        // Store selected sprint data
        (window as any).currentSprintData = selectedSprint;
        
        // Show the modal
        (window as any).showModal();
        
        // Load sprint issues
        (window as any).loadSprintIssues(sprintId);
      } else {
        console.error('Sprint not found:', sprintId);
      }
    }
  }
  
  // Make functions available globally
  (window as any).openSprintModal = openSprintModal;
  
  // Event listeners
  document.addEventListener('boardChanged', handleBoardChange as unknown as EventListener);
  document.addEventListener('DOMContentLoaded', initializePage);
</script>

<style>
  .velocity-page {
    @apply max-w-7xl mx-auto;
  }
  
  .velocity-dashboard {
    @apply relative;
  }
</style>
