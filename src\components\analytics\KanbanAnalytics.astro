---
/**
 * Kanban Analytics Component
 * Following Clean Code: Single responsibility - Kanban-specific cycle time analytics
 * Loads cycle time data dynamically with progress loading
 */

import KanbanProgressLoader from '../ui/KanbanProgressLoader.astro';
import PeriodSelector from '../ui/PeriodSelector.astro';
import IssueTypeSelector from '../ui/IssueTypeSelector.astro';
import KanbanIssuesModal from './KanbanIssuesModal.astro';

export interface Props {
  boardId: string | null;
  loading?: boolean;
}

const { boardId, loading = false } = Astro.props;
---

<div class="kanban-analytics" data-board-id={boardId}>
  <!-- Filter Selection Section -->
  <div class="filter-selection-section mb-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">
        Analytics Filters
      </h3>
      
      <!-- Filters Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Period Selector -->
        <div>
          <PeriodSelector
            selectedPeriod="last-15-days"
            disabled={!boardId || loading}
            class="period-selector-component"
          />
        </div>
        
        <!-- Issue Type Selector -->
        <div>
          <IssueTypeSelector
            disabled={!boardId || loading}
            class="issue-type-selector-component"
          />
        </div>
      </div>
      
      <!-- Apply Filters Button -->
      <div class="mt-6 flex justify-end">
        <button
          type="button"
          id="apply-filters-button"
          class="bg-purple-600 text-white px-6 py-2 rounded-md text-sm font-medium
                 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
                 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          Apply Selection
        </button>
      </div>
    </div>
  </div>

  <!-- Kanban Analytics Content -->
  <div class="kanban-content-section">
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
      <!-- Main Cycle Time Chart -->
      <div class="xl:col-span-2">
        <div id="cycle-time-chart-container">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Cycle Time Analysis
            </h3>
            
            {loading && (
              <div id="initial-loading" class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div class="h-32 bg-gray-200 rounded mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            )}
            
            <div id="cycle-time-data" class="hidden">
              <!-- Cycle Time Percentiles -->
              <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="text-center p-4 bg-red-50 rounded-lg cursor-help" 
                     title="50% of issues are completed within this time - Fastest performance">
                  <div class="text-2xl font-bold text-red-600" id="p50-value">-</div>
                  <div class="text-sm text-red-800">50th percentile</div>
                  <div class="text-xs text-gray-600">days</div>
                </div>
                <div class="text-center p-4 bg-yellow-50 rounded-lg cursor-help" 
                     title="75% of issues are completed within this time">
                  <div class="text-2xl font-bold text-yellow-600" id="p75-value">-</div>
                  <div class="text-sm text-yellow-800">75th percentile</div>
                  <div class="text-xs text-gray-600">days</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg cursor-help" 
                     title="85% of issues are completed within this time">
                  <div class="text-2xl font-bold text-green-600" id="p85-value">-</div>
                  <div class="text-sm text-green-800">85th percentile</div>
                  <div class="text-xs text-gray-600">days</div>
                </div>
                <div class="text-center p-4 bg-green-100 rounded-lg cursor-help" 
                     title="95% of issues are completed within this time - Slowest performance">
                  <div class="text-2xl font-bold text-green-800" id="p95-value">-</div>
                  <div class="text-sm text-green-900">95th percentile</div>
                  <div class="text-xs text-gray-600">days</div>
                </div>
              </div>
              
              <!-- Summary Stats -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-gray-600">Total Issues:</span>
                    <span class="font-medium ml-2" id="total-issues">-</span>
                  </div>
                  <div>
                    <span class="text-gray-600">Completed Issues:</span>
                    <span class="font-medium ml-2" id="completed-issues">-</span>
                  </div>
                  <div>
                    <span class="text-gray-600">Sample Size:</span>
                    <span class="font-medium ml-2" id="sample-size">-</span>
                  </div>
                  <div>
                    <span class="text-gray-600">Last Updated:</span>
                    <span class="font-medium ml-2" id="last-updated">-</span>
                  </div>
                </div>
              </div>
              
              <!-- Cycle Time Probability Table -->
              <div id="cycle-time-probability" class="mt-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">Cycle Time Probability Distribution</h4>
                <div class="overflow-x-auto">
                  <table class="w-full border-collapse bg-white probability-table">
                    <thead>
                      <tr class="bg-gray-50">
                        <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900 w-20">
                          Days
                        </th>
                        <th class="border border-gray-200 px-3 py-3 text-left text-sm font-medium text-gray-900 w-20">
                          Issues Count
                        </th>
                        <th class="border border-gray-200 px-3 py-3 text-left text-sm font-medium text-gray-900 w-30">
                          Probability that an issue is going to take this long
                        </th>
                        <th class="border border-gray-200 px-3 py-3 text-left text-sm font-medium text-gray-900 w-30">
                          Confidence that an issue will take this long or less
                        </th>
                      </tr>
                    </thead>
                    <tbody id="probability-table-body">
                      <!-- Table rows will be populated dynamically -->
                    </tbody>
                  </table>
                </div>
                
                <!-- Recommendations -->
                <div id="probability-recommendations" class="mt-4 p-4 bg-blue-50 rounded-lg">
                  <div class="flex items-start">
                    <div class="text-blue-500 mr-3 mt-1">💡</div>
                    <div>
                      <h5 class="font-medium text-blue-900 mb-1">Recommendation</h5>
                      <p class="text-sm text-blue-800" id="recommendation-text">
                        Loading recommendations...
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div id="cycle-time-placeholder" class={!loading ? "text-center py-12" : "hidden"}>
              <div class="text-6xl mb-4">⏱️</div>
              <h4 class="text-xl font-medium text-gray-900 mb-2">
                Select a Board
              </h4>
              <p class="text-gray-600 mb-6">
                Choose a Kanban board to view cycle time percentiles and flow metrics.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Current Flow Metrics -->
      <div class="xl:col-span-1">
        <div id="flow-metrics-container">
          {loading ? (
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                Flow Metrics
              </h3>
              <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-1/3 mb-3"></div>
                <div class="h-8 bg-gray-200 rounded mb-4"></div>
                <div class="grid grid-cols-2 gap-4">
                  <div class="h-16 bg-gray-200 rounded"></div>
                  <div class="h-16 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ) : (
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                Flow Metrics
              </h3>
              <div class="text-center py-8">
                <div class="text-4xl mb-3">📊</div>
                <p class="text-gray-600 text-sm">
                  Flow metrics will show throughput, WIP limits, and cycle time trends.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    
    <!-- Kanban Insights Section -->
    <div class="mt-8">
      <div id="kanban-insights-container">
        {loading ? (
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Flow Insights
            </h3>
            <div class="animate-pulse">
              <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="h-24 bg-gray-200 rounded"></div>
                <div class="h-24 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        ) : (
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Flow Insights
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="insight-card p-4 bg-purple-50 rounded-lg">
                <div class="flex items-start">
                  <div class="text-purple-500 mr-3 mt-1">🔄</div>
                  <div>
                    <h5 class="font-medium text-purple-900 mb-1">Continuous Flow</h5>
                    <p class="text-sm text-purple-800">
                      Kanban boards focus on continuous delivery and flow optimization rather than sprint-based velocity.
                    </p>
                  </div>
                </div>
              </div>
              <div class="insight-card p-4 bg-green-50 rounded-lg">
                <div class="flex items-start">
                  <div class="text-green-500 mr-3 mt-1">⚡</div>
                  <div>
                    <h5 class="font-medium text-green-900 mb-1">Cycle Time Focus</h5>
                    <p class="text-sm text-green-800">
                      Measure how long it takes to complete work items from start to finish.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  </div>
  
  <!-- Issues Details Section -->
  <div class="issues-details-section mt-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">
        Issues Used for Percentile Calculation
      </h3>
      
      <div id="issues-details-content" class="hidden">
        <!-- Summary Info -->
        <div class="mb-4 p-4 bg-blue-50 rounded-lg">
          <div class="flex items-center text-sm text-blue-800">
            <div class="text-blue-500 mr-2">📊</div>
            <span>Showing <span id="issues-count" class="font-medium">0</span> completed issues used for cycle time percentiles calculation</span>
          </div>
        </div>
        
        <!-- Issues Table -->
        <div class="overflow-x-auto">
          <table class="w-full border-collapse bg-white">
            <thead>
              <tr class="bg-gray-50">
                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                  Type
                </th>
                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                  Issue Key
                </th>
                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                  Summary
                </th>
                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                  Status
                </th>
                <th class="border border-gray-200 px-4 py-3 text-left text-sm font-medium text-gray-900">
                  Cycle Time
                </th>
              </tr>
            </thead>
            <tbody id="issues-details-table-body">
              <!-- Table rows will be populated dynamically -->
            </tbody>
          </table>
        </div>
        
        <!-- Empty State -->
        <div id="issues-details-empty" class="hidden text-center py-8">
          <div class="text-4xl mb-2">📋</div>
          <p class="text-gray-600">No completed issues found for the selected filters.</p>
        </div>
      </div>
      
      <!-- Placeholder when no board selected -->
      <div id="issues-details-placeholder" class="text-center py-8">
        <div class="text-4xl mb-2">📝</div>
        <p class="text-gray-600">Select a board and apply filters to see the issues used for cycle time calculations.</p>
      </div>
    </div>
  </div>

  <!-- Kanban Issues Modal -->
  <KanbanIssuesModal isOpen={false} />
</div>

<style>
  .kanban-analytics {
    @apply w-full;
  }
  
  .filter-selection-section {
    @apply w-full;
  }
  
  .kanban-content-section {
    @apply w-full;
  }
  
  .insight-card {
    @apply transition-transform duration-200;
  }
  
  .insight-card:hover {
    @apply transform scale-105;
  }
  
  .probability-table {
    @apply border-collapse w-full;
  }
  
  .probability-row {
    @apply transition-colors duration-200;
  }
  
  .probability-row:hover {
    @apply bg-gray-50;
  }
  
  .recommended-range {
    @apply border-2 border-dashed border-orange-400;
    background-color: rgba(251, 191, 36, 0.1);
  }
  
  .recommended-range td {
    @apply font-medium;
  }
  
  .confidence-cell {
    @apply transition-all duration-300;
  }
</style>

<script>
  import { kanbanState, type KanbanAnalyticsData } from '../../lib/events/kanban-events';
  
  // Kanban Analytics Client-side Logic
  // Following Clean Code: Express intent, single responsibility per function
  
  // BoardId will be read from data attribute when DOM is ready
  let boardId: string | null = null;
  let isLoading = false;
  let cancelController: AbortController | null = null;
  let currentPeriod: string = 'last-15-days';
  let currentCustomRange: { start: string; end: string } | null = null;
  let currentIssueTypes: string[] = [];
  let allIssuesData: KanbanAnalyticsData['issuesDetails'] = []; // Store all issues data in memory
  let currentProbabilityData: KanbanAnalyticsData['cycleTimeProbability'] | null = null; // Store probability data
  
  /**
   * Shows global loading overlay with progress tracking
   * Following Clean Code: Clear function name, single responsibility
   */
  function showLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.classList.remove('hidden');
    }
  }
  
  /**
   * Hides global loading overlay
   * Following Clean Code: Clear function name, single responsibility
   */
  function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.classList.add('hidden');
    }
  }
  
  /**
   * Updates progress loader with current stage and progress
   * Following Clean Code: Express intent, clear parameters
   */
  function updateProgressLoader(stage: string, progress: number, message: string, issuesProcessed?: number, totalIssues?: number) {
    const container = document.getElementById('progress-loader-container');
    if (!container) return;

    // Create updated progress loader HTML for Kanban
    const progressHTML = createKanbanProgressLoaderHTML({
      stage,
      progress,
      message,
      issuesProcessed,
      totalIssues
    });
    container.innerHTML = progressHTML;
  }

  /**
   * Creates HTML for the Kanban progress loader component
   * Following Clean Code: Template generation, express intent
   */
  function createKanbanProgressLoaderHTML(progressData: any): string {
    const { stage, progress, message, issuesProcessed, totalIssues } = progressData;

    return `
      <div class="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
        <div class="text-center">
          <div class="text-lg font-semibold text-gray-900 mb-2">Loading Kanban Analytics</div>
          <div class="text-sm text-gray-600 mb-4">${message}</div>

          <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div class="bg-purple-500 h-3 rounded-full transition-all duration-300" style="width: ${progress}%"></div>
          </div>

          <div class="text-xs text-gray-500">
            Stage: ${stage} • ${progress}% complete
            ${issuesProcessed && totalIssues ? ` • ${issuesProcessed}/${totalIssues} issues` : ''}
          </div>
          
          <button 
            onclick="window.kanbanLoader.cancel()"
            class="mt-4 px-4 py-2 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    `;
  }
  
  /**
   * Displays cycle time data in the UI
   * Following Clean Code: Single responsibility, clear data flow
   */
  function displayCycleTimeData(data: KanbanAnalyticsData) {
    console.log('[KanbanAnalytics] Displaying cycle time data for board:', data.boardId);
    
    // Store all issues data and probability data in memory for modal filtering
    allIssuesData = data.issuesDetails;
    currentProbabilityData = data.cycleTimeProbability || null;
    
    // Update kanban state with new data
    kanbanState.updateAnalyticsData(data);
    
    console.log('[KanbanAnalytics] Stored', allIssuesData.length, 'issues and probability data in memory');
    
    // Find the cycle time chart container
    const chartContainer = document.querySelector('#cycle-time-chart-container');
    if (!chartContainer) {
      console.error('[KanbanAnalytics] Cycle time chart container not found');
      return;
    }
    
    // Hide loading, placeholder and show data within the chart container
    const initialLoading = chartContainer.querySelector('#initial-loading');
    const placeholder = chartContainer.querySelector('#cycle-time-placeholder');
    const dataContainer = chartContainer.querySelector('#cycle-time-data');
    
    if (initialLoading) initialLoading.classList.add('hidden');
    if (placeholder) placeholder.classList.add('hidden');
    if (dataContainer) dataContainer.classList.remove('hidden');
    
    // Update percentile values within the chart container
    const updateElement = (id: string, value: number) => {
      const element = chartContainer.querySelector(`#${id}`);
      if (element) {
        // Convert hours to days (24 hours per day)
        const days = value / 24;
        element.textContent = days.toFixed(1);
      }
    };
    
    updateElement('p50-value', data.cycleTimePercentiles.p50);
    updateElement('p75-value', data.cycleTimePercentiles.p75);
    updateElement('p85-value', data.cycleTimePercentiles.p85);
    updateElement('p95-value', data.cycleTimePercentiles.p95);
    
    // Update summary stats within the chart container
    const updateTextElement = (id: string, value: string | number) => {
      const element = chartContainer.querySelector(`#${id}`);
      if (element) {
        element.textContent = value.toString();
      }
    };
    
    updateTextElement('total-issues', data.totalIssues);
    updateTextElement('completed-issues', data.completedIssues);
    updateTextElement('sample-size', data.cycleTimePercentiles.sampleSize);
    updateTextElement('last-updated', new Date(data.calculatedAt).toLocaleString());
    
    // Display probability table if available
    if (data.cycleTimeProbability) {
      displayProbabilityTable(data.cycleTimeProbability, chartContainer);
    }
    
    // Display issues details table
    displayIssuesDetails(data.issuesDetails);
    
    console.log('[KanbanAnalytics] Cycle time data displayed successfully');
  }
  
  /**
   * Displays the cycle time probability table
   * Following Clean Code: Single responsibility, clear data flow
   */
  function displayProbabilityTable(
    probabilityData: KanbanAnalyticsData['cycleTimeProbability'], 
    chartContainer: Element
  ) {
    if (!probabilityData) return;
    
    console.log('[KanbanAnalytics] Displaying probability table');
    
    const tableBody = chartContainer.querySelector('#probability-table-body');
    if (!tableBody) {
      console.error('[KanbanAnalytics] Probability table body not found');
      return;
    }
    
    // Clear existing rows
    tableBody.innerHTML = '';
    
    // Generate table rows
    probabilityData.dayRanges.forEach((range) => {
      const row = document.createElement('tr');
      row.className = `probability-row ${range.isRecommended ? 'recommended-range' : ''}`;
      
      // Determine confidence level classes using Tailwind directly
      let confidenceClasses = 'bg-red-50 text-red-800'; // < 50%
      if (range.confidence >= 75) {
        confidenceClasses = 'bg-green-50 text-green-800'; // >= 75%
      } else if (range.confidence >= 50) {
        confidenceClasses = 'bg-yellow-50 text-yellow-800'; // 50-74%
      }
      
      row.innerHTML = `
        <td class="border border-gray-200 px-4 py-3 text-sm font-medium text-gray-900 w-20">
          ${range.range}
        </td>
        <td class="border border-gray-200 px-3 py-3 text-sm text-gray-700 w-20 text-center">
          <button 
            class="issue-count-button text-blue-600 hover:text-blue-800 font-medium hover:underline cursor-pointer transition-colors"
            onclick="handleIssueCountClick(${range.minDays}, ${range.maxDays})"
            title="Click to view issues in this cycle time range"
          >
            ${range.count}
          </button>
        </td>
        <td class="border border-gray-200 px-3 py-3 text-sm text-gray-700 w-30">
          ${range.probability}%
        </td>
        <td class="border border-gray-200 px-3 py-3 text-sm confidence-cell transition-all duration-300 ${confidenceClasses} w-30">
          ${range.confidence}%
        </td>
      `;
      
      // Add tooltip for recommended ranges
      if (range.isRecommended) {
        row.title = `Recommended range: ${range.count} issues completed in this timeframe`;
      }
      
      tableBody.appendChild(row);
    });
    
    // Update recommendations
    const recommendationText = chartContainer.querySelector('#recommendation-text');
    if (recommendationText && probabilityData.recommendations) {
      const { minDays, maxDays, confidenceLevel } = probabilityData.recommendations;
      recommendationText.textContent = 
        `For optimal planning, expect ${minDays}-${maxDays} days for completion with ${confidenceLevel}% confidence. Highlighted ranges show the most common completion times.`;
    }
  }
  
  /**
   * Displays issues details table
   * Following Clean Code: Single responsibility, clear data flow
   */
  function displayIssuesDetails(issuesDetails: KanbanAnalyticsData['issuesDetails']) {
    console.log('[KanbanAnalytics] Displaying issues details table');
    
    // Find containers
    const contentContainer = document.querySelector('#issues-details-content');
    const placeholderContainer = document.querySelector('#issues-details-placeholder');
    const emptyContainer = document.querySelector('#issues-details-empty');
    const tableBody = document.querySelector('#issues-details-table-body');
    const issuesCount = document.querySelector('#issues-count');
    
    if (!contentContainer || !placeholderContainer || !emptyContainer || !tableBody || !issuesCount) {
      console.error('[KanbanAnalytics] Issues details containers not found');
      return;
    }
    
    // Hide placeholder
    placeholderContainer.classList.add('hidden');
    
    if (issuesDetails.length === 0) {
      // Show empty state
      contentContainer.classList.add('hidden');
      emptyContainer.classList.remove('hidden');
      return;
    }
    
    // Show content and update count
    contentContainer.classList.remove('hidden');
    emptyContainer.classList.add('hidden');
    issuesCount.textContent = issuesDetails.length.toString();
    
    // Clear existing table rows
    tableBody.innerHTML = '';
    
    // Populate table rows
    issuesDetails.forEach(issue => {
      const row = document.createElement('tr');
      row.className = 'hover:bg-gray-50';
      
      // Get status category color
      const getStatusCategoryColor = (categoryName: string) => {
        switch (categoryName) {
          case 'To Do': return 'bg-gray-100 text-gray-800';
          case 'In Progress': return 'bg-blue-100 text-blue-800';
          case 'Done': return 'bg-green-100 text-green-800';
          default: return 'bg-gray-100 text-gray-800';
        }
      };
      
      const statusColorClass = getStatusCategoryColor(issue.status.statusCategory.name);
      const cycleTimeDisplay = issue.cycleTimeDays ? `${issue.cycleTimeDays.toFixed(1)} days` : '-';
      
      row.innerHTML = `
        <td class="border border-gray-200 px-4 py-3">
          <div class="flex items-center">
            <img src="${issue.issueType.iconUrl}" alt="${issue.issueType.name}" class="w-4 h-4 mr-2" />
            <span class="text-sm font-medium text-gray-900">${issue.issueType.name}</span>
          </div>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <a href="${issue.jiraUrl}" target="_blank" rel="noopener noreferrer" 
             class="text-blue-600 hover:text-blue-800 font-medium text-sm hover:underline">
            ${issue.key}
          </a>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <div class="text-sm text-gray-900 max-w-md truncate" title="${issue.summary}">
            ${issue.summary}
          </div>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColorClass}">
            ${issue.status.name}
          </span>
        </td>
        <td class="border border-gray-200 px-4 py-3">
          <span class="text-sm font-medium text-gray-900">${cycleTimeDisplay}</span>
        </td>
      `;
      
      tableBody.appendChild(row);
    });
    
    console.log(`[KanbanAnalytics] Issues details table populated with ${issuesDetails.length} issues`);
  }

  /**
   * Shows error state
   * Following Clean Code: Express intent, consistent error handling
   */
  function showErrorState(error: string) {
    console.error('[KanbanAnalytics] Error loading analytics:', error);
    
    // Find the chart container
    const chartContainer = document.querySelector('#cycle-time-chart-container');
    if (!chartContainer) {
      console.error('[KanbanAnalytics] Cycle time chart container not found');
      return;
    }
    
    const initialLoading = chartContainer.querySelector('#initial-loading');
    const placeholder = chartContainer.querySelector('#cycle-time-placeholder');
    const dataContainer = chartContainer.querySelector('#cycle-time-data');
    
    if (initialLoading) initialLoading.classList.add('hidden');
    if (dataContainer) dataContainer.classList.add('hidden');
    if (placeholder) {
      placeholder.classList.remove('hidden');
      placeholder.innerHTML = `
        <div class="text-center py-12">
          <div class="text-6xl mb-4">❌</div>
          <h4 class="text-xl font-medium text-gray-900 mb-2">
            Error Loading Analytics
          </h4>
          <p class="text-gray-600 mb-6">
            ${error}
          </p>
          <button
            onclick="window.kanbanAnalytics.loadKanbanAnalytics(window.kanbanAnalytics.getCurrentBoardId())"
            class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      `;
    }
    
    // Hide issues details table on error
    hideIssuesDetails();
  }
  
  /**
   * Loads Kanban analytics data for a specific board with optional filters
   * Following Clean Code: Express intent, async/await pattern
   */
  async function loadKanbanAnalytics(boardId: string, period?: string, customRange?: { start: string; end: string }, issueTypes?: string[]) {
    if (!boardId) return;
    
    // Update current state
    if (period) currentPeriod = period;
    if (customRange) currentCustomRange = customRange;
    if (issueTypes) currentIssueTypes = issueTypes;
    
    // Enable filters now that we have a board
    updateFiltersState(boardId, false);
    
    // Load issue types if not already loaded or board changed
    await loadIssueTypesForBoard(boardId);
    
    // Mark this as a board change (don't load data automatically)
    console.log('[KanbanAnalytics] Board updated to:', boardId, '- ready for manual data loading');
    
    // Cancel any existing request
    if (cancelController) {
      cancelController.abort();
    }
    
    isLoading = true;
    updateFiltersState(boardId, true); // Disable during loading
    cancelController = new AbortController();
    
    console.log(`[KanbanAnalytics] Loading analytics for board: ${boardId}, period: ${currentPeriod}, issueTypes: [${currentIssueTypes.join(', ')}]`);
    
    try {
      showLoadingOverlay();
      updateProgressLoader('issues', 10, 'Fetching board issues...');
      
      // Build API URL with filters
      const url = new URL(`/api/kanban/${boardId}/analytics`, window.location.origin);
      url.searchParams.set('period', currentPeriod);
      
      if (currentPeriod === 'custom' && currentCustomRange) {
        url.searchParams.set('start', currentCustomRange.start);
        url.searchParams.set('end', currentCustomRange.end);
      }
      
      if (currentIssueTypes.length > 0) {
        url.searchParams.set('issueTypes', currentIssueTypes.join(','));
      }
      
      const response = await fetch(url.toString(), {
        signal: cancelController.signal
      });
      
      updateProgressLoader('analysis', 50, 'Calculating cycle time percentiles...');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data: KanbanAnalyticsData = await response.json();
      
      updateProgressLoader('complete', 100, 'Analysis complete!');
      
      // Small delay to show completion state
      setTimeout(() => {
        hideLoadingOverlay();
        displayCycleTimeData(data);
      }, 500);
      
    } catch (error) {
      hideLoadingOverlay();
      
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('[KanbanAnalytics] Request cancelled');
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      showErrorState(errorMessage);
    } finally {
      isLoading = false;
      updateFiltersState(boardId, false); // Re-enable after loading
      cancelController = null;
    }
  }
  
  /**
   * Cancels current loading operation
   * Following Clean Code: Express intent, clean cancellation
   */
  function cancelLoading() {
    if (cancelController) {
      cancelController.abort();
      hideLoadingOverlay();
    }
  }
  
  /**
   * Handles period change events from PeriodSelector
   * Following Clean Code: Event-driven architecture, clear intent
   */
  function handlePeriodChange(event: CustomEvent) {
    const { period, customRange } = event.detail;
    
    if (!boardId) return;
    
    console.log('[KanbanAnalytics] Period changed:', { period, customRange });
    
    // Update current state but don't reload automatically
    currentPeriod = period;
    currentCustomRange = customRange;
  }
  
  /**
   * Handles apply filters button click
   * Following Clean Code: Express intent, unified action
   */
  function handleApplyFilters() {
    if (!boardId) return;
    
    // Get current issue types selection
    const issueTypeSelector = (window as any).issueTypeSelector;
    if (issueTypeSelector) {
      currentIssueTypes = issueTypeSelector.getSelectedIssueTypes();
    }
    
    // Get custom date range if period is custom
    if (currentPeriod === 'custom') {
      const startInput = document.getElementById('custom-start') as HTMLInputElement;
      const endInput = document.getElementById('custom-end') as HTMLInputElement;
      
      if (startInput && endInput) {
        const startDate = startInput.value;
        const endDate = endInput.value;
        
        // Basic validation
        if (!startDate || !endDate) {
          alert('Please select both start and end dates');
          return;
        }
        
        if (new Date(startDate) > new Date(endDate)) {
          alert('Start date must be before end date');
          return;
        }
        
        currentCustomRange = { start: startDate, end: endDate };
      }
    } else {
      currentCustomRange = null;
    }
    
    console.log('[KanbanAnalytics] Applying filters:', { 
      period: currentPeriod, 
      customRange: currentCustomRange,
      issueTypes: currentIssueTypes 
    });
    
    // Reload analytics with current filters
    loadKanbanAnalytics(boardId, currentPeriod, currentCustomRange || undefined, currentIssueTypes);
  }
  
  /**
   * Loads issue types for a specific board
   * Following Clean Code: Express intent, async operation
   */
  async function loadIssueTypesForBoard(boardId: string) {
    const issueTypeSelector = (window as any).issueTypeSelector;
    if (issueTypeSelector) {
      await issueTypeSelector.loadIssueTypes(boardId);
      // Get all issue types as default selection
      currentIssueTypes = issueTypeSelector.getSelectedIssueTypes();
    }
  }
  
  /**
   * Updates all filter components state based on board selection
   * Following Clean Code: Single responsibility, state management
   */
  function updateFiltersState(boardId: string | null, loading: boolean = false) {
    console.log(`[KanbanAnalytics] updateFiltersState called with boardId: ${boardId}, loading: ${loading}`);

    // Update period selector
    updatePeriodSelectorState(boardId, loading);

    // Update issue type selector
    const issueTypeSelector = (window as any).issueTypeSelector;
    if (issueTypeSelector) {
      issueTypeSelector.updateIssueTypeSelectorState(boardId, loading);
    } else {
      console.warn('[KanbanAnalytics] issueTypeSelector not found on window');
    }

    // Update apply button
    const applyButton = document.getElementById('apply-filters-button') as HTMLButtonElement;
    console.log('[KanbanAnalytics] Apply button element:', applyButton);

    if (applyButton) {
      const shouldDisable = !boardId || loading;
      applyButton.disabled = shouldDisable;
      console.log(`[KanbanAnalytics] Apply button disabled set to: ${shouldDisable}`);
      console.log(`[KanbanAnalytics] Apply button actual disabled state: ${applyButton.disabled}`);
    } else {
      console.error('[KanbanAnalytics] Apply button not found in updateFiltersState!');
    }

    console.log(`[KanbanAnalytics] Filters ${!boardId || loading ? 'disabled' : 'enabled'} (boardId: ${boardId}, loading: ${loading})`);
  }
  
  /**
   * Updates PeriodSelector state based on board selection
   * Following Clean Code: Single responsibility, DOM manipulation
   */
  function updatePeriodSelectorState(boardId: string | null, loading: boolean = false) {
    const periodSelect = document.getElementById('period-select') as HTMLSelectElement;
    const customStartInput = document.getElementById('custom-start') as HTMLInputElement;
    const customEndInput = document.getElementById('custom-end') as HTMLInputElement;
    const applyButton = document.getElementById('apply-custom-dates') as HTMLButtonElement;
    
    const shouldDisable = !boardId || loading;
    
    // Update all period selector elements
    [periodSelect, customStartInput, customEndInput, applyButton].forEach(element => {
      if (element) {
        element.disabled = shouldDisable;
      }
    });
    
    console.log(`[KanbanAnalytics] PeriodSelector ${shouldDisable ? 'disabled' : 'enabled'} (boardId: ${boardId}, loading: ${loading})`);
  }

  // Setup event listeners
  document.addEventListener('periodChanged', handlePeriodChange as EventListener);
  
  // Initialize filters state on page load
  document.addEventListener('DOMContentLoaded', () => {
    // Read boardId from data attribute
    const kanbanContainer = document.querySelector('.kanban-analytics') as HTMLElement;
    boardId = kanbanContainer?.dataset.boardId || null;

    console.log('[KanbanAnalytics] DOMContentLoaded - boardId from data attribute:', boardId);
    console.log('[KanbanAnalytics] Container found:', !!kanbanContainer);
    console.log('[KanbanAnalytics] Dataset:', kanbanContainer?.dataset);

    // Setup apply filters button using event delegation (most reliable method)
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (target && target.id === 'apply-filters-button') {
        handleApplyFilters();
      }
    });

    // Initialize component with current boardId
    initializeComponent();

    console.log('[KanbanAnalytics] Initialized without automatic data loading - use Apply Selection button to load data');
  });
  
  /**
   * Initializes component with current boardId
   * Following Clean Code: Separate concerns, clear intent
   */
  function initializeComponent() {
    if (boardId) {
      updateFiltersState(boardId, false);
      loadIssueTypesForBoard(boardId);
      console.log('[KanbanAnalytics] Board set to:', boardId, '- ready for manual data loading via Apply Selection');
    } else {
      updateFiltersState(null, false);
      console.log('[KanbanAnalytics] No board available - no data will be loaded');
    }
  }

  /**
   * Gets current boardId
   * Following Clean Code: Simple getter function
   */
  function getCurrentBoardId(): string | null {
    return boardId;
  }

  /**
   * Updates board state when board selection changes
   * Following Clean Code: Separate concerns, clear intent
   */
  function setBoardId(newBoardId: string | null) {
    console.log('[KanbanAnalytics] setBoardId called with:', newBoardId);
    console.log('[KanbanAnalytics] Previous boardId was:', boardId);

    boardId = newBoardId;

    if (boardId) {
      console.log('[KanbanAnalytics] Updating filters state for boardId:', boardId);
      updateFiltersState(boardId, false);
      loadIssueTypesForBoard(boardId);
      console.log('[KanbanAnalytics] Board set to:', boardId, '- ready for manual data loading via Apply Selection');
    } else {
      console.log('[KanbanAnalytics] Clearing board - disabling filters');
      updateFiltersState(null, false);
      console.log('[KanbanAnalytics] Board cleared - no data will be loaded');
    }
  }

  // Export functions for parent component access
  (window as any).kanbanAnalytics = {
    loadKanbanAnalytics,
    cancelLoading,
    updateFiltersState, // Export for external board selection events
    getCurrentBoardId, // Export for template access
    setBoardId // Export for board changes without automatic loading
  };
  
  // Export for global access (used by progress loader retry button)
  (window as any).kanbanLoader = {
    cancel: cancelLoading
  };
  
  /**
   * Hides issues details table
   * Following Clean Code: Single responsibility
   */
  function hideIssuesDetails() {
    const contentContainer = document.querySelector('#issues-details-content');
    const placeholderContainer = document.querySelector('#issues-details-placeholder');
    const emptyContainer = document.querySelector('#issues-details-empty');
    
    if (contentContainer) contentContainer.classList.add('hidden');
    if (emptyContainer) emptyContainer.classList.add('hidden');
    if (placeholderContainer) placeholderContainer.classList.remove('hidden');
  }
  
  /**
   * Handles click on issue count in probability table
   * Opens modal with issues for specific cycle time range
   * Following Clean Code: Express intent, event handling
   */
  function handleIssueCountClick(minDays: number, maxDays: number) {
    console.log('[KanbanAnalytics] Opening issues modal for cycle time range:', minDays, '-', maxDays, 'days');

    // Use the kanban state manager to show the modal
    kanbanState.showIssuesModal({ minDays, maxDays });
  }
  
  // Make function available globally for the HTML onclick handlers
  // TODO: In a future refactor, this could be replaced with event delegation
  (window as any).handleIssueCountClick = handleIssueCountClick;
</script>
